import {
  Input,
  Output,
  Conversion,
  ALL_FORMATS,
  BlobSource,
  BufferTarget,
  Mp4OutputFormat,
  WebMOutputFormat,
  CanvasSource,
  AudioBufferSource,
  AudioSampleSink,
  QUALITY_HIGH,
  QUALITY_MEDIUM,
  QUALITY_LOW
} from 'mediabunny';
import { ExportOptions } from '@/components/ExportDialog';

interface VideoClip {
  id: string;
  name: string;
  duration: number;
  startTime: number;
  endTime: number;
  file: File;
  url: string;
  sourceStartTime?: number;
  sourceEndTime?: number;
  muted?: boolean;
}

interface ZoomEffect {
  id: string;
  startTime: number;
  endTime: number;
  zoomLevel: number;
  clipId: string;
}

interface Subtitle {
  id: string;
  text: string;
  startTime: number;
  endTime: number;
  fontSize: number;
  fontColor: string;
  backgroundColor: string;
  position: 'top' | 'center' | 'bottom';
  fontFamily: string;
}

export class MediaBunnyExporter {
  private canvas: HTMLCanvasElement | null = null;
  private ctx: CanvasRenderingContext2D | null = null;
  private videoElements: Map<string, HTMLVideoElement> = new Map();
  private audioInputs: Map<string, Input> = new Map();
  private audioSinks: Map<string, AudioSampleSink> = new Map();
  private audioContext: AudioContext | null = null;

  async exportVideo(
    videoClips: VideoClip[],
    zoomEffects: ZoomEffect[],
    subtitles: Subtitle[],
    options: ExportOptions,
    onProgress: (progress: number) => void
  ): Promise<Blob> {
    console.log('=== STARTING MEDIABUNNY EXPORT ===');
    
    try {
      // Calculate timeline bounds
      const timelineStart = Math.min(...videoClips.map(clip => clip.startTime));
      const timelineEnd = Math.max(...videoClips.map(clip => clip.endTime));
      const timelineDuration = timelineEnd - timelineStart;
      
      console.log('Timeline:', { timelineStart, timelineEnd, timelineDuration });

      // Setup canvas for video composition
      const { width, height } = this.getCanvasDimensions(options.resolution);
      this.canvas = document.createElement('canvas');
      this.canvas.width = width;
      this.canvas.height = height;
      this.ctx = this.canvas.getContext('2d');

      if (!this.ctx) {
        throw new Error('Failed to get canvas context');
      }

      // Initialize single AudioContext for all audio processing
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();

      // Initialize video elements and audio inputs
      await this.initializeVideoElements(videoClips);
      await this.initializeAudioInputs(videoClips);

      // Create MediaBunny output
      const output = new Output({
        format: this.getOutputFormat(options.format),
        target: new BufferTarget(),
      });

      // Add video track from canvas
      const videoSource = new CanvasSource(this.canvas, {
        codec: this.getVideoCodec(options.format),
        bitrate: this.getBitrate(options.quality, options.resolution),
      });
      output.addVideoTrack(videoSource);

      // Add audio track
      const audioSource = await this.setupAudioSource(videoClips, options);
      if (audioSource) {
        output.addAudioTrack(audioSource);
        console.log('Audio track added to MediaBunny output');
      } else {
        console.log('No audio source available');
      }

      // Start the output
      await output.start();

      // Render video sequence
      await this.renderVideoSequence(
        videoClips,
        zoomEffects,
        subtitles,
        timelineStart,
        timelineDuration,
        options.framerate,
        videoSource,
        onProgress
      );

      // Finalize the output
      await output.finalize();

      // Get the final blob
      const buffer = output.target.buffer;
      const blob = new Blob([buffer], { type: this.getMimeType(options.format) });
      
      console.log(`Export complete! Blob size: ${blob.size} bytes`);
      this.cleanup();
      
      return blob;
    } catch (error) {
      console.error('MediaBunny export error:', error);
      this.cleanup();
      throw error;
    }
  }

  private getOutputFormat(format: string) {
    switch (format.toLowerCase()) {
      case 'webm':
        return new WebMOutputFormat();
      case 'mp4':
      default:
        return new Mp4OutputFormat();
    }
  }

  private getVideoCodec(format: string): string {
    switch (format.toLowerCase()) {
      case 'webm':
        return 'vp9';
      case 'mp4':
      default:
        return 'avc'; // H.264
    }
  }

  private getCanvasDimensions(resolution: string): { width: number; height: number } {
    switch (resolution) {
      case '720p':
        return { width: 1280, height: 720 };
      case '1080p':
        return { width: 1920, height: 1080 };
      case '1440p':
        return { width: 2560, height: 1440 };
      case '4k':
        return { width: 3840, height: 2160 };
      default:
        return { width: 1280, height: 720 };
    }
  }

  private getBitrate(quality: string, resolution: string): number {
    const baseRates = {
      '720p': { low: 1000000, medium: 2500000, high: 5000000 },
      '1080p': { low: 2000000, medium: 5000000, high: 8000000 },
      '1440p': { low: 4000000, medium: 8000000, high: 12000000 },
      '4k': { low: 8000000, medium: 15000000, high: 25000000 },
    };

    return baseRates[resolution as keyof typeof baseRates]?.[quality as keyof typeof baseRates['720p']] || 2500000;
  }

  private getMimeType(format: string): string {
    switch (format.toLowerCase()) {
      case 'webm':
        return 'video/webm';
      case 'mp4':
      default:
        return 'video/mp4';
    }
  }

  private async initializeVideoElements(videoClips: VideoClip[]): Promise<void> {
    console.log('Initializing video elements...');
    
    for (const clip of videoClips) {
      const video = document.createElement('video');
      video.src = clip.url;
      video.muted = false; // Don't mute - we need audio for extraction
      video.crossOrigin = 'anonymous';
      video.preload = 'auto'; // Preload more data for better frame access
      video.playsInline = true;
      video.loop = false;
      
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error(`Video setup timeout: ${clip.name}`));
        }, 10000);
        
        video.onloadedmetadata = () => {
          clearTimeout(timeout);
          console.log(`Video loaded: ${clip.name}, duration: ${video.duration}`);
          resolve();
        };
        
        video.onerror = (e) => {
          clearTimeout(timeout);
          console.error(`Video error for ${clip.name}:`, e);
          reject(new Error(`Video setup failed: ${clip.name}`));
        };
      });

      this.videoElements.set(clip.id, video);
    }
  }

  private async initializeAudioInputs(videoClips: VideoClip[]): Promise<void> {
    console.log('Initializing audio inputs...');

    for (const clip of videoClips) {
      if (clip.muted) continue;

      try {
        // Create MediaBunny input for audio processing
        const input = new Input({
          formats: ALL_FORMATS,
          source: new BlobSource(clip.file)
        });

        // Get the primary audio track
        const audioTrack = await input.getPrimaryAudioTrack();
        if (audioTrack) {
          const canDecode = await audioTrack.canDecode();
          if (canDecode) {
            const audioSink = new AudioSampleSink(audioTrack);
            this.audioInputs.set(clip.id, input);
            this.audioSinks.set(clip.id, audioSink);
            console.log(`Audio initialized for clip: ${clip.name}`);
          } else {
            console.warn(`Cannot decode audio for clip: ${clip.name}`);
          }
        } else {
          console.warn(`No audio track found for clip: ${clip.name}`);
        }
      } catch (error) {
        console.warn(`Audio initialization failed for ${clip.name}:`, error);
      }
    }
  }

  private async setupAudioSource(videoClips: VideoClip[], options: ExportOptions): Promise<AudioBufferSource | null> {
    console.log('Setting up MediaBunny audio source...');

    // Check if we have any clips with audio
    const audioClips = videoClips.filter(clip => !clip.muted);
    if (audioClips.length === 0) {
      console.log('No audio clips found, skipping audio');
      return null;
    }

    try {
      // Create audio source for output
      const audioSource = new AudioBufferSource({
        codec: this.getAudioCodec(options.format),
        bitrate: this.getAudioBitrate(options.quality),
        sampleRate: 44100,
        numberOfChannels: 2
      });

      console.log(`Audio source created with codec: ${this.getAudioCodec(options.format)}, bitrate: ${this.getAudioBitrate(options.quality)}`);

      // Process audio continuously - this will complete before returning
      await this.processAudioContinuously(videoClips, audioSource, options);

      return audioSource;
    } catch (error) {
      console.error('Failed to setup audio source:', error);
      return null;
    }
  }

  private getAudioCodec(format: string): string {
    switch (format.toLowerCase()) {
      case 'webm':
        return 'opus';
      case 'mp4':
      default:
        return 'aac';
    }
  }

  private getAudioBitrate(quality: string): number {
    switch (quality.toLowerCase()) {
      case 'low':
        return 96000;
      case 'medium':
        return 128000;
      case 'high':
      default:
        return 192000;
    }
  }

  private async renderVideoSequence(
    videoClips: VideoClip[],
    zoomEffects: ZoomEffect[],
    subtitles: Subtitle[],
    timelineStart: number,
    timelineDuration: number,
    framerate: number,
    videoSource: CanvasSource,
    onProgress: (progress: number) => void
  ): Promise<void> {
    console.log('Starting video sequence rendering...');

    const frameInterval = 1 / framerate;
    const totalFrames = Math.ceil(timelineDuration * framerate);
    let currentFrame = 0;

    for (let frame = 0; frame < totalFrames; frame++) {
      const currentTime = timelineStart + (frame * frameInterval);

      // Render frame to canvas FIRST
      await this.renderFrame(currentTime, videoClips, zoomEffects, subtitles);

      // THEN add the current canvas state to MediaBunny
      // The timestamp is when this frame should appear in the video
      // The duration is how long this frame should be displayed
      await videoSource.add(currentTime, frameInterval);

      // Audio is now processed continuously, not frame-by-frame

      currentFrame++;
      const progress = (currentFrame / totalFrames) * 100;
      onProgress(Math.min(progress, 100));

      // Small delay to prevent blocking the UI
      if (currentFrame % 5 === 0) {
        await new Promise(resolve => setTimeout(resolve, 1));
      }
    }

    // Close the video source to indicate we're done
    videoSource.close();
    // Audio source is already closed in processAudioContinuously

    console.log('Video sequence rendering complete');
  }

  private async renderFrame(
    currentTime: number,
    videoClips: VideoClip[],
    zoomEffects: ZoomEffect[],
    subtitles: Subtitle[]
  ): Promise<void> {
    if (!this.ctx || !this.canvas) return;

    // Clear canvas
    this.ctx.fillStyle = '#000000';
    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

    // Find active clip
    const activeClip = videoClips.find(clip => 
      currentTime >= clip.startTime && currentTime < clip.endTime
    );

    if (!activeClip) return;

    const video = this.videoElements.get(activeClip.id);
    if (!video) return;

    // Calculate source time
    const clipProgress = (currentTime - activeClip.startTime) / (activeClip.endTime - activeClip.startTime);
    const sourceStartTime = activeClip.sourceStartTime || 0;
    const sourceEndTime = activeClip.sourceEndTime || video.duration;
    const sourceDuration = sourceEndTime - sourceStartTime;
    const targetSourceTime = sourceStartTime + (clipProgress * sourceDuration);

    // Set video time - be more precise for MediaBunny
    const timeDiff = Math.abs(video.currentTime - targetSourceTime);
    if (timeDiff > 0.1) { // More precise seeking for better frame accuracy
      video.currentTime = targetSourceTime;
      // Wait for seek to complete and video to be ready
      await new Promise(resolve => {
        const checkReady = () => {
          if (video.readyState >= 2) { // HAVE_CURRENT_DATA
            resolve(undefined);
          } else {
            setTimeout(checkReady, 10);
          }
        };
        checkReady();
      });
    }

    // Apply zoom effect
    const zoomLevel = this.getCurrentZoomLevel(currentTime, activeClip.id, zoomEffects);
    const scale = zoomLevel / 100;
    
    this.ctx.save();
    
    if (scale !== 1) {
      const offsetX = (this.canvas.width * (1 - scale)) / 2;
      const offsetY = (this.canvas.height * (1 - scale)) / 2;
      this.ctx.translate(offsetX, offsetY);
      this.ctx.scale(scale, scale);
    }

    // Draw video frame with proper error handling
    try {
      const videoWidth = video.videoWidth;
      const videoHeight = video.videoHeight;

      // Ensure video is ready and has valid dimensions
      if (videoWidth > 0 && videoHeight > 0 && video.readyState >= 2) {
        const videoAspectRatio = videoWidth / videoHeight;
        const canvasAspectRatio = this.canvas.width / this.canvas.height;

        let drawWidth, drawHeight, drawX, drawY;

        if (videoAspectRatio > canvasAspectRatio) {
          // Video is wider than canvas
          drawWidth = this.canvas.width;
          drawHeight = this.canvas.width / videoAspectRatio;
          drawX = 0;
          drawY = (this.canvas.height - drawHeight) / 2;
        } else {
          // Video is taller than canvas
          drawWidth = this.canvas.height * videoAspectRatio;
          drawHeight = this.canvas.height;
          drawX = (this.canvas.width - drawWidth) / 2;
          drawY = 0;
        }

        // Draw the video frame
        this.ctx.drawImage(video, drawX, drawY, drawWidth, drawHeight);

        console.log(`Drew frame at ${currentTime.toFixed(2)}s, video time: ${video.currentTime.toFixed(2)}s`);
      } else {
        // Video not ready or invalid dimensions, draw black frame
        console.warn(`Video not ready at ${currentTime.toFixed(2)}s, readyState: ${video.readyState}, dimensions: ${videoWidth}x${videoHeight}`);
        this.ctx.fillStyle = '#000000';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
      }
    } catch (error) {
      console.warn('Frame draw error:', error);
      // Draw black frame as fallback
      this.ctx.fillStyle = '#000000';
      this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }
    
    this.ctx.restore();
    
    // Render subtitles
    const activeSubtitle = subtitles.find(subtitle => 
      currentTime >= subtitle.startTime && currentTime < subtitle.endTime
    );
    
    if (activeSubtitle) {
      this.renderSubtitle(activeSubtitle);
    }
  }

  private getCurrentZoomLevel(currentTime: number, clipId: string, zoomEffects: ZoomEffect[]): number {
    const activeZoomEffect = zoomEffects.find(effect => 
      effect.clipId === clipId &&
      currentTime >= effect.startTime && 
      currentTime <= effect.endTime
    );
    return activeZoomEffect ? activeZoomEffect.zoomLevel : 100;
  }

  private renderSubtitle(subtitle: Subtitle): void {
    if (!this.ctx || !this.canvas) return;

    this.ctx.save();
    
    // Set font
    this.ctx.font = `${subtitle.fontSize}px ${subtitle.fontFamily}`;
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    
    // Calculate text position
    const centerX = this.canvas.width / 2;
    let y: number;
    
    switch (subtitle.position) {
      case 'top':
        y = subtitle.fontSize + 20;
        break;
      case 'center':
        y = this.canvas.height / 2;
        break;
      case 'bottom':
      default:
        y = this.canvas.height - subtitle.fontSize - 20;
        break;
    }
    
    // Draw background
    const textMetrics = this.ctx.measureText(subtitle.text);
    const textWidth = textMetrics.width;
    const textHeight = subtitle.fontSize;
    
    this.ctx.fillStyle = subtitle.backgroundColor;
    this.ctx.fillRect(
      centerX - textWidth / 2 - 10,
      y - textHeight / 2 - 5,
      textWidth + 20,
      textHeight + 10
    );
    
    // Draw text
    this.ctx.fillStyle = subtitle.fontColor;
    this.ctx.fillText(subtitle.text, centerX, y);
    
    this.ctx.restore();
  }

  private async processAudioContinuously(
    videoClips: VideoClip[],
    audioSource: AudioBufferSource,
    options: ExportOptions
  ): Promise<void> {
    console.log('Processing audio continuously...');

    // Get timeline bounds
    const timelineStart = Math.min(...videoClips.map(clip => clip.startTime));
    const timelineEnd = Math.max(...videoClips.map(clip => clip.endTime));
    const timelineDuration = timelineEnd - timelineStart;

    console.log(`Audio timeline: ${timelineStart}s to ${timelineEnd}s (${timelineDuration}s)`);

    // Sort clips by start time for sequential processing
    const sortedClips = [...videoClips].sort((a, b) => a.startTime - b.startTime);

    let currentTimelinePosition = timelineStart;

    for (const clip of sortedClips) {
      // Add silence if there's a gap before this clip
      if (currentTimelinePosition < clip.startTime) {
        const gapDuration = clip.startTime - currentTimelinePosition;
        console.log(`Adding silence gap: ${gapDuration.toFixed(3)}s`);
        const silenceBuffer = this.createSilenceBuffer(gapDuration);
        await audioSource.add(silenceBuffer);
        currentTimelinePosition = clip.startTime;
      }

      if (clip.muted) {
        // Add silence for muted clips
        const clipDuration = clip.endTime - clip.startTime;
        console.log(`Adding silence for muted clip: ${clip.name}, duration: ${clipDuration.toFixed(3)}s`);
        const silenceBuffer = this.createSilenceBuffer(clipDuration);
        await audioSource.add(silenceBuffer);
        currentTimelinePosition = clip.endTime;
        continue;
      }

      const audioSink = this.audioSinks.get(clip.id);
      if (!audioSink) {
        console.warn(`No audio sink for clip: ${clip.name}, adding silence`);
        const clipDuration = clip.endTime - clip.startTime;
        const silenceBuffer = this.createSilenceBuffer(clipDuration);
        await audioSource.add(silenceBuffer);
        currentTimelinePosition = clip.endTime;
        continue;
      }

      try {
        // Calculate the source time range for this clip
        const sourceStartTime = clip.sourceStartTime || 0;
        const sourceEndTime = clip.sourceEndTime || clip.duration;

        console.log(`Processing audio for clip: ${clip.name}, timeline: ${clip.startTime.toFixed(3)}s-${clip.endTime.toFixed(3)}s, source: ${sourceStartTime.toFixed(3)}s-${sourceEndTime.toFixed(3)}s`);

        // Use MediaBunny's samples iterator for continuous processing
        for await (const audioSample of audioSink.samples(sourceStartTime, sourceEndTime)) {
          if (!audioSample || audioSample.duration <= 0) continue;

          // Convert to AudioBuffer without distortion
          const audioBuffer = audioSample.toAudioBuffer();

          // Add the buffer directly without stretching/compressing
          await audioSource.add(audioBuffer);

          console.log(`Added audio sample: ${audioSample.timestamp.toFixed(3)}s, duration: ${audioSample.duration.toFixed(3)}s`);
        }

        currentTimelinePosition = clip.endTime;
        console.log(`Completed audio processing for clip: ${clip.name}`);
      } catch (error) {
        console.error(`Error processing audio for clip ${clip.name}:`, error);
        // Add silence for the clip duration to maintain sync
        const clipDuration = clip.endTime - clip.startTime;
        const silenceBuffer = this.createSilenceBuffer(clipDuration);
        await audioSource.add(silenceBuffer);
        currentTimelinePosition = clip.endTime;
      }
    }

    // Close the audio source to indicate we're done adding audio data
    audioSource.close();
    console.log('Audio processing completed');
  }

  private createSilenceBuffer(duration: number): AudioBuffer {
    if (!this.audioContext) {
      throw new Error('AudioContext not initialized');
    }

    const sampleRate = 44100;
    const numberOfChannels = 2;
    const length = Math.ceil(duration * sampleRate);

    const buffer = this.audioContext.createBuffer(numberOfChannels, length, sampleRate);

    // Fill with silence (zeros)
    for (let channel = 0; channel < numberOfChannels; channel++) {
      const channelData = buffer.getChannelData(channel);
      channelData.fill(0);
    }

    return buffer;
  }

  // Removed createExactDurationBuffer and trimAudioBuffer methods
  // These were causing audio distortion by stretching/compressing samples

  private cleanup(): void {
    console.log('Cleaning up MediaBunny exporter...');

    this.videoElements.forEach(video => {
      // Remove event listeners to prevent errors during cleanup
      video.onloadedmetadata = null;
      video.onerror = null;
      video.pause();
      // Don't clear src or reload during cleanup to avoid errors
    });
    this.videoElements.clear();

    // Clean up audio resources
    this.audioInputs.clear();
    this.audioSinks.clear();

    // Clean up AudioContext
    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }

    this.canvas = null;
    this.ctx = null;
  }

  downloadBlob(blob: Blob, filename: string): void {
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }
}
